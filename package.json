{"name": "xipchat", "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "check": "svelte-check --tsconfig ./tsconfig.json"}, "devDependencies": {"@sveltejs/vite-plugin-svelte": "^6.1.4", "@tailwindcss/postcss": "^4.1.12", "@tsconfig/svelte": "^5.0.5", "@types/chrome": "^0.1.6", "@types/marked": "^5.0.2", "@types/node": "^22.18.1", "daisyui": "^5.1.6", "postcss": "^8.5.6", "svelte": "^5.38.6", "svelte-check": "^4.3.1", "tailwindcss": "^4.1.12", "tslib": "^2.8.1", "typescript": "^5.9.2", "vite": "^7.1.4"}, "dependencies": {"groq-sdk": "^0.31.0", "marked": "^16.2.1"}}