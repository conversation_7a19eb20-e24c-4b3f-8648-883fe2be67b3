@import "tailwindcss";

@plugin "daisyui" {
  themes: light --default, dark --prefersdark, cupcake;
}

@custom-variant dark (&:is(.dark *));

@theme {
  --color-logo-purple: #8457f7;
}

/*
  The default border color has changed to `currentcolor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentcolor);
  }
}

body {
  width: 100%;
  height: 100vh;
  margin: 0;
  padding: 0;
  background-color: #f5f1ff;
}
