import { createWorker, type Worker } from "tesseract.js";

export interface OCRResult {
  text: string;
  confidence: number;
}

export interface OCRProgress {
  status: string;
  progress: number;
}

export class OCRService {
  private worker: Worker | null = null;
  private isInitialized = false;
  private initializationPromise: Promise<void> | null = null;

  constructor() {
    // Initialize worker lazily
  }

  /**
   * Initialize the OCR worker
   */
  private async initializeWorker(): Promise<void> {
    if (this.isInitialized) return;

    if (this.initializationPromise) {
      return this.initializationPromise;
    }

    this.initializationPromise = this.doInitialize();
    return this.initializationPromise;
  }

  private async doInitialize(): Promise<void> {
    try {
      this.worker = await createWorker("eng");

      this.isInitialized = true;
      console.log("OCR worker initialized successfully");
    } catch (error) {
      console.error("Failed to initialize OCR worker:", error);
      this.worker = null;
      this.isInitialized = false;
      this.initializationPromise = null;
      throw error;
    }
  }

  /**
   * Extract text from an image
   */
  async extractText(
    imageData: string | HTMLImageElement | HTMLCanvasElement
  ): Promise<OCRResult> {
    try {
      await this.initializeWorker();

      if (!this.worker) {
        throw new Error("OCR worker not initialized");
      }

      const result = await this.worker.recognize(imageData);

      return {
        text: result.data.text.trim(),
        confidence: result.data.confidence,
      };
    } catch (error) {
      console.error("OCR extraction failed:", error);
      throw new Error(
        `OCR failed: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  /**
   * Extract text with preprocessing for better accuracy
   */
  async extractTextWithPreprocessing(imageData: string): Promise<OCRResult> {
    try {
      // Preprocess the image for better OCR accuracy
      const preprocessedImage = await this.preprocessImage(imageData);
      return await this.extractText(preprocessedImage);
    } catch (error) {
      console.error("OCR with preprocessing failed:", error);
      // Fallback to original image if preprocessing fails
      return await this.extractText(imageData);
    }
  }

  /**
   * Preprocess image for better OCR accuracy
   */
  private async preprocessImage(imageData: string): Promise<HTMLCanvasElement> {
    return new Promise((resolve, reject) => {
      const img = new Image();

      img.onload = () => {
        try {
          const canvas = document.createElement("canvas");
          const ctx = canvas.getContext("2d");

          if (!ctx) {
            reject(new Error("Could not get canvas context"));
            return;
          }

          // Set canvas size
          canvas.width = img.width;
          canvas.height = img.height;

          // Draw original image
          ctx.drawImage(img, 0, 0);

          // Get image data for processing
          const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
          const data = imageData.data;

          // Apply preprocessing filters
          for (let i = 0; i < data.length; i += 4) {
            // Convert to grayscale
            const gray = Math.round(
              0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2]
            );

            // Apply contrast enhancement and thresholding
            const enhanced = gray > 128 ? 255 : 0;

            data[i] = enhanced; // Red
            data[i + 1] = enhanced; // Green
            data[i + 2] = enhanced; // Blue
            // Alpha channel remains unchanged
          }

          // Put processed image data back
          ctx.putImageData(imageData, 0, 0);
          resolve(canvas);
        } catch (error) {
          reject(error);
        }
      };

      img.onerror = () => {
        reject(new Error("Failed to load image for preprocessing"));
      };

      img.src = imageData;
    });
  }

  /**
   * Check if text is likely present in the image
   */
  async hasText(imageData: string): Promise<boolean> {
    try {
      const result = await this.extractText(imageData);
      // Consider text present if we have reasonable confidence and non-empty text
      return result.text.length > 3 && result.confidence > 30;
    } catch (error) {
      console.error("Error checking for text:", error);
      return false;
    }
  }

  /**
   * Terminate the OCR worker
   */
  async terminate(): Promise<void> {
    if (this.worker) {
      await this.worker.terminate();
      this.worker = null;
      this.isInitialized = false;
      this.initializationPromise = null;
    }
  }

  /**
   * Get OCR status
   */
  getStatus(): { initialized: boolean; ready: boolean } {
    return {
      initialized: this.isInitialized,
      ready: this.worker !== null,
    };
  }
}

// Create a singleton instance
export const ocrService = new OCRService();

// Cleanup on page unload
if (typeof window !== "undefined") {
  window.addEventListener("beforeunload", () => {
    ocrService.terminate();
  });
}
