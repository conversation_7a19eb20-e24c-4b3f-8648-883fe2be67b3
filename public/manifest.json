{"manifest_version": 3, "name": "ClipChat - Llama4 + Groq", "version": "1.0.0", "description": "A side panel Chrome extension for chatting with Llama4 multi-modal, accelerated by Groq | Fast AI Inference", "permissions": ["sidePanel", "storage", "activeTab", "scripting", "tabs"], "host_permissions": ["<all_urls>"], "action": {"default_title": "Open ClipChat", "default_icon": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "64": "icons/icon64.png", "128": "icons/icon128.png"}}, "side_panel": {"default_path": "index.html"}, "icons": {"16": "icons/icon16.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}, "content_scripts": [{"matches": ["<all_urls>"], "js": ["content-script.js"], "run_at": "document_idle"}], "background": {"service_worker": "background.js", "type": "module"}}